"""Tool implementations for the LangGraph agent."""

import smtplib
import logging
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from typing import Optional, List, Dict, Any
from langchain_core.tools import tool

from config.settings import settings
from agent.state import EmailMessage

logger = logging.getLogger(__name__)


@tool
def create_email(to_email: str, subject: str, body: str, from_name: Optional[str] = None) -> str:
    """
    Create an email message that will be queued for sending.

    This tool creates an EmailMessage object instead of directly sending the email.
    The email will be added to the pending emails list and shown to the user for approval.

    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Email body content
        from_name: Optional sender name (defaults to configured email)

    Returns:
        Confirmation message in German with email details
    """
    try:
        # Create email message object
        email_message = EmailMessage(
            to_email=to_email,
            subject=subject,
            body=body,
            from_name=from_name
        )

        # Return structured data that can be parsed by the node
        # We use a special format that the node can recognize and extract
        return f"EMAIL_CREATED:{email_message.model_dump_json()}"

    except Exception as e:
        error_msg = f"<PERSON><PERSON> beim <PERSON> der Email: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def get_file_attachment(attachment_id: str) -> str:
    """
    Retrieve information about a file attachment from an email.

    This tool allows the agent to access file attachments that were received via email.
    It returns information about the attachment including its path, so the agent can
    read or process the file content.

    Args:
        attachment_id: The unique ID of the attachment to retrieve

    Returns:
        Information about the attachment in German, including file path and metadata
    """
    try:
        # Initialize database connection (import here to avoid circular import)
        from email_agent.database import EmailDatabase
        database = EmailDatabase()

        # Get attachment metadata
        attachment = database.get_attachment_by_id(attachment_id)

        if not attachment:
            return f"Anhang mit ID '{attachment_id}' wurde nicht gefunden."

        # Check if file still exists
        file_path = attachment['file_path']
        if not os.path.exists(file_path):
            return f"Anhang-Datei '{attachment['filename']}' wurde nicht gefunden (Pfad: {file_path})."

        # Return attachment information
        file_size_mb = attachment['file_size'] / (1024 * 1024)

        return f"""Anhang gefunden:
- Dateiname: {attachment['filename']}
- Dateipfad: {file_path}
- Dateityp: {attachment['content_type']}
- Dateigröße: {file_size_mb:.2f} MB
- Klassifikation: {attachment['classification_reason']}

Die Datei kann über den angegebenen Pfad gelesen werden."""

    except Exception as e:
        error_msg = f"Fehler beim Abrufen des Anhangs: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def list_email_attachments(email_context: Optional[str] = None) -> str:
    """
    List all available email attachments that the agent can access.

    This tool shows all file attachments from processed emails that are classified
    as relevant (not logos or signatures).

    Args:
        email_context: Optional context to filter attachments (not implemented yet)

    Returns:
        List of available attachments with their IDs and basic information in German
    """
    try:
        # Initialize database connection (import here to avoid circular import)
        from email_agent.database import EmailDatabase
        database = EmailDatabase()

        # Get all relevant attachments using database method
        import sqlite3
        try:
            with sqlite3.connect(database.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT unique_id, filename, content_type, file_size, email_uid
                    FROM email_attachments
                    WHERE is_relevant = TRUE
                    ORDER BY created_at DESC
                    LIMIT 20
                """)

                rows = cursor.fetchall()

                if not rows:
                    return "Keine E-Mail-Anhänge verfügbar."

                attachment_list = ["Verfügbare E-Mail-Anhänge:"]
                for row in rows:
                    unique_id, filename, content_type, file_size, email_uid = row
                    file_size_mb = file_size / (1024 * 1024)
                    attachment_list.append(
                        f"- ID: {unique_id} | {filename} ({content_type}, {file_size_mb:.2f} MB) [E-Mail: {email_uid}]"
                    )

                return "\n".join(attachment_list)
        except Exception as db_error:
            logger.error(f"Database query failed: {db_error}")
            return f"Fehler beim Datenbankzugriff: {str(db_error)}"

    except Exception as e:
        error_msg = f"Fehler beim Auflisten der Anhänge: {str(e)}"
        logger.error(error_msg)
        return error_msg


# List of available tools for the agent
TOOLS = [create_email, get_file_attachment, list_email_attachments]
