"""Tests for attachment-related agent tools."""

import os
import tempfile
import unittest
from unittest.mock import Mock, patch

from agent.tools import get_file_attachment, list_email_attachments
from email_agent.database import EmailDatabase


class TestAttachmentTools(unittest.TestCase):
    """Test cases for attachment tools."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, 'test.db')
        
        # Create test database with sample data
        self.database = EmailDatabase(self.db_path)
        self.database.init_database()
        
        # Add test attachment
        test_file_path = os.path.join(self.temp_dir, 'test_document.pdf')
        with open(test_file_path, 'w') as f:
            f.write("Test document content")
        
        attachments = [{
            'filename': 'test_document.pdf',
            'safe_filename': 'abc123_test_document.pdf',
            'file_path': test_file_path,
            'content_type': 'application/pdf',
            'file_size': 20,
            'unique_id': 'abc123',
            'is_relevant': True,
            'classification_reason': 'Document file - likely relevant'
        }]
        
        self.database.save_email_attachments('test_email_123', attachments)
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @patch('agent.tools.EmailDatabase')
    def test_get_file_attachment_success(self, mock_db_class):
        """Test successful file attachment retrieval."""
        # Mock database
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        
        # Create test file
        test_file_path = os.path.join(self.temp_dir, 'test.pdf')
        with open(test_file_path, 'w') as f:
            f.write("test content")
        
        mock_db.get_attachment_by_id.return_value = {
            'filename': 'test.pdf',
            'file_path': test_file_path,
            'content_type': 'application/pdf',
            'file_size': 12,
            'classification_reason': 'Document file'
        }
        
        result = get_file_attachment.invoke({'attachment_id': 'abc123'})
        
        self.assertIn('Anhang gefunden', result)
        self.assertIn('test.pdf', result)
        self.assertIn(test_file_path, result)
    
    @patch('agent.tools.EmailDatabase')
    def test_get_file_attachment_not_found(self, mock_db_class):
        """Test file attachment not found."""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_attachment_by_id.return_value = None
        
        result = get_file_attachment.invoke({'attachment_id': 'nonexistent'})
        
        self.assertIn('wurde nicht gefunden', result)
    
    @patch('agent.tools.EmailDatabase')
    @patch('agent.tools.sqlite3')
    def test_list_email_attachments(self, mock_sqlite, mock_db_class):
        """Test listing email attachments."""
        # Mock database connection
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_sqlite.connect.return_value.__enter__.return_value = mock_conn
        
        # Mock database instance
        mock_db = Mock()
        mock_db.db_path = self.db_path
        mock_db_class.return_value = mock_db
        
        # Mock query results
        mock_cursor.fetchall.return_value = [
            ('abc123', 'test.pdf', 'application/pdf', 1024, 'email_123'),
            ('def456', 'document.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 2048, 'email_456')
        ]
        
        result = list_email_attachments.invoke({})
        
        self.assertIn('Verfügbare E-Mail-Anhänge', result)
        self.assertIn('test.pdf', result)
        self.assertIn('document.docx', result)
        self.assertIn('abc123', result)
        self.assertIn('def456', result)
    
    @patch('agent.tools.EmailDatabase')
    @patch('agent.tools.sqlite3')
    def test_list_email_attachments_empty(self, mock_sqlite, mock_db_class):
        """Test listing when no attachments available."""
        # Mock database connection
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_sqlite.connect.return_value.__enter__.return_value = mock_conn
        
        # Mock database instance
        mock_db = Mock()
        mock_db.db_path = self.db_path
        mock_db_class.return_value = mock_db
        
        # Mock empty results
        mock_cursor.fetchall.return_value = []
        
        result = list_email_attachments.invoke({})
        
        self.assertIn('Keine E-Mail-Anhänge verfügbar', result)


if __name__ == '__main__':
    unittest.main()
